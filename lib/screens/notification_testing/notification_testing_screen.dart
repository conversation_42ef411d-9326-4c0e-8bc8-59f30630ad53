import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/models/auth_model.dart';
import 'package:railops/core/utilities/comman_functions.dart';
import 'package:railops/services/notification_services/notification_integration_helper.dart';
import 'package:railops/services/notification_services/ca_notification_test_service.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/firestore_token_service.dart';
import 'package:railops/utils/notification_test_runner.dart';
import 'package:railops/services/profile_services/profile_train_services.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/types/profile_types/add_trains_response.dart';

class NotificationTestingScreen extends StatefulWidget {
  const NotificationTestingScreen({super.key});

  @override
  State<NotificationTestingScreen> createState() =>
      _NotificationTestingScreenState();
}

class _NotificationTestingScreenState extends State<NotificationTestingScreen> {
  final TextEditingController _tokenController = TextEditingController();
  final TextEditingController _latController =
      TextEditingController(text: '28.6139'); // New Delhi
  final TextEditingController _lngController =
      TextEditingController(text: '77.2090'); // New Delhi

  bool _isLoading = false;
  String _lastTestResult = '';
  Map<String, bool> _testResults = {};
  bool _isLoadingUserToken = true;
  String _userTokenStatus = 'Loading...';

  // CA/CS/EHK testing configuration
  final String _selectedStation = 'New Delhi';
  final String _trainNumber = 'TEST12345';
  final String _userId = 'test_ca_001';
  final List<String> _selectedCoaches = ['A1', 'B3'];
  final Map<String, String> _testCoordinates = {
    'New Delhi': '28.6139,77.2090',
    'ARA': '25.5500,84.6667',
    'BTA': '25.2167,84.3667',
    'DNR': '25.4167,85.0167',
    'PNBE': '25.5941,85.1376',
    'RJPB': '25.6093,85.1947',
    'PNC': '25.6167,85.2167',
  };

  @override
  void initState() {
    super.initState();
    _loadUserToken();
  }

  /// Load the current user's authentication token
  Future<void> _loadUserToken() async {
    setState(() {
      _isLoadingUserToken = true;
      _userTokenStatus = 'Loading user token...';
    });

    try {
      // Check if user is authenticated
      final authModel = Provider.of<AuthModel>(context, listen: false);
      if (!authModel.isAuthenticated) {
        setState(() {
          _isLoadingUserToken = false;
          _userTokenStatus = '❌ User not authenticated. Please login first.';
          _tokenController.text = '';
        });
        return;
      }

      // Get user token using CommanFunctions utility
      final userToken = await CommanFunctions().getToken();

      if (userToken.isNotEmpty) {
        setState(() {
          _tokenController.text = userToken;
          _isLoadingUserToken = false;
          _userTokenStatus = '✅ User token loaded successfully';
        });

        if (kDebugMode) {
          print('🔑 User token loaded for notification testing');
          print('Token length: ${userToken.length} characters');
          print(
              'Token preview: ${userToken.substring(0, userToken.length > 20 ? 20 : userToken.length)}...');
        }
      } else {
        setState(() {
          _isLoadingUserToken = false;
          _userTokenStatus = '❌ No user token found. Please login again.';
          _tokenController.text = '';
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingUserToken = false;
        _userTokenStatus = '❌ Error loading user token: $e';
        _tokenController.text = '';
      });

      if (kDebugMode) {
        print('❌ Error loading user token: $e');
      }
    }
  }

  @override
  void dispose() {
    _tokenController.dispose();
    _latController.dispose();
    _lngController.dispose();
    super.dispose();
  }

  /// Check user's current train assignment status and suggest working coordinates
  Future<void> _checkUserTrainAssignment() async {
    try {
      if (kDebugMode) {
        print('🔍 Checking user train assignment status...');
      }

      // Check if user has "inside train" status enabled
      final insideTrainStatus = await ProfileTrainServices.getInsideTrainStatus(
          _tokenController.text);
      final isInsideTrain = insideTrainStatus['inside_train'] ?? false;
      final insideTrainNumber = insideTrainStatus['inside_train_number'] ?? '';
      final insideTrainDate = insideTrainStatus['inside_train_date'] ?? '';

      // Get user's assigned trains
      final trainDetails =
          await TrainService.getTrainDetails(_tokenController.text);

      if (kDebugMode) {
        print('🚂 Inside Train Status: $isInsideTrain');
        print('🚂 Inside Train Number: $insideTrainNumber');
        print('🚂 Inside Train Date: $insideTrainDate');
        print(
            '🚂 User Assigned Trains: ${trainDetails.trainDetails.keys.join(', ')}');
      }

      // Update the test result with train assignment information
      String assignmentInfo = '''
🔍 TRAIN ASSIGNMENT STATUS:

Inside Train Status: ${isInsideTrain ? '✅ YES' : '❌ NO'}
Current Train: ${insideTrainNumber.isNotEmpty ? insideTrainNumber : 'None'}
Train Date: ${insideTrainDate.isNotEmpty ? insideTrainDate : 'None'}

Assigned Trains: ${trainDetails.trainDetails.isNotEmpty ? trainDetails.trainDetails.entries.map((e) => '${e.key} (${e.value.originDate})').join(', ') : 'None'}

💡 COORDINATE RECOMMENDATIONS:
${_generateCoordinateRecommendations(isInsideTrain, insideTrainNumber, trainDetails)}
''';

      setState(() {
        _lastTestResult =
            '$assignmentInfo\n${_lastTestResult.contains('Testing API integration...') ? 'Proceeding with API call...' : _lastTestResult}';
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking train assignment: $e');
      }

      setState(() {
        _lastTestResult = '''❌ Error checking train assignment: $e

Proceeding with API call using current coordinates...
''';
      });
    }
  }

  /// Generate coordinate recommendations based on user's train assignment
  String _generateCoordinateRecommendations(
      bool isInsideTrain, String trainNumber, dynamic trainDetails) {
    if (isInsideTrain && trainNumber.isNotEmpty) {
      return '''
✅ You are marked as "inside train $trainNumber"
- Try coordinates along the route of train $trainNumber
- Use coordinates near stations where this train is currently scheduled
- The API should return data if the train is active and you're near its route
''';
    } else if (trainDetails.trainDetails.isNotEmpty) {
      final assignedTrains = trainDetails.trainDetails.keys.join(', ');
      return '''
⚠️ You have assigned trains ($assignedTrains) but are not marked as "inside train"
- Enable "Inside Train" status in your profile
- Select one of your assigned trains: $assignedTrains
- Use coordinates along the route of your assigned train
''';
    } else {
      return '''
❌ No train assignments found
- You need to be assigned to a train first
- Contact admin to assign you to a train
- Or use test coordinates for development purposes
- Current coordinates may return "No Train Assigned" error
''';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Testing'),
        backgroundColor: Colors.blue.shade50,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildConfigurationCard(),
            const SizedBox(height: 16),
            _buildApiIntegrationCard(),
            const SizedBox(height: 16),
            _buildPhase1TestsCard(),
            const SizedBox(height: 16),
            _buildPhase2TestsCard(),
            const SizedBox(height: 16),
            _buildCATestsCard(),
            const SizedBox(height: 16),
            _buildQuickTestsCard(),
            const SizedBox(height: 16),
            _buildTestResultsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Test Configuration',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: _isLoadingUserToken ? null : _loadUserToken,
                  icon: _isLoadingUserToken
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.refresh),
                  tooltip: 'Refresh user token',
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Token status indicator
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _userTokenStatus.startsWith('✅')
                    ? Colors.green.shade50
                    : _userTokenStatus.startsWith('❌')
                        ? Colors.red.shade50
                        : Colors.blue.shade50,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: _userTokenStatus.startsWith('✅')
                      ? Colors.green.shade200
                      : _userTokenStatus.startsWith('❌')
                          ? Colors.red.shade200
                          : Colors.blue.shade200,
                ),
              ),
              child: Text(
                _userTokenStatus,
                style: TextStyle(
                  fontSize: 12,
                  color: _userTokenStatus.startsWith('✅')
                      ? Colors.green.shade700
                      : _userTokenStatus.startsWith('❌')
                          ? Colors.red.shade700
                          : Colors.blue.shade700,
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _tokenController,
              decoration: const InputDecoration(
                labelText: 'User Authentication Token',
                hintText: 'User token will be loaded automatically',
                border: OutlineInputBorder(),
                helperText: 'This token is used for API authentication',
              ),
              readOnly: true, // Make it read-only since it's auto-loaded
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _latController,
                    decoration: const InputDecoration(
                      labelText: 'Latitude',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _lngController,
                    decoration: const InputDecoration(
                      labelText: 'Longitude',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Default coordinates: New Delhi (28.6139, 77.2090)',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildApiIntegrationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'API Integration Tests',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _testApiIntegration,
                icon: const Icon(Icons.api),
                label: const Text('Test Real API Call'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Calls https://railops-uat-api.biputri.com/api/onboarding_details_popup/ and triggers notifications based on response',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhase1TestsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Phase 1 Notification Types',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildTestButton(
              'Boarding Alert',
              'Test boarding notification',
              Icons.train,
              () => _testPhase1Notification('boarding'),
              Colors.blue,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'Off-boarding Alert',
              'Test off-boarding notification',
              Icons.exit_to_app,
              () => _testPhase1Notification('offboarding'),
              Colors.orange,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'Station Approaching',
              'Test station approach notification',
              Icons.location_on,
              () => _testPhase1Notification('approaching'),
              Colors.purple,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'Coach Reminder',
              'Test coach reminder notification',
              Icons.directions_railway,
              () => _testPhase1Notification('coach'),
              Colors.teal,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'Berth Reminder',
              'Test berth reminder notification',
              Icons.bed,
              () => _testPhase1Notification('berth'),
              Colors.indigo,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhase2TestsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Phase 2 Enhanced Types',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildTestButton(
              'Proximity Alert',
              'Test proximity-based notification',
              Icons.near_me,
              () => _testPhase2Notification('proximity'),
              Colors.red,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'Station Approach Alert',
              'Test enhanced station approach',
              Icons.schedule,
              () => _testPhase2Notification('approach'),
              Colors.amber,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'Train Status Update',
              'Test train status notification',
              Icons.info,
              () => _testPhase2Notification('status'),
              Colors.cyan,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'Boarding Count Update',
              'Test boarding count notification',
              Icons.people,
              () => _testPhase2Notification('count'),
              Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCATestsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'CA/CS/EHK Train Journey Tests',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Station: $_selectedStation | Train: $_trainNumber | Coaches: ${_selectedCoaches.join(', ')}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            _buildTestButton(
              'Multi-Station Proximity',
              'Test full route: ARA → BTA → DNR → PNBE → RJPB → PNC',
              Icons.route,
              () => _testCAMultiStationProximity(),
              Colors.deepOrange,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'Multi-Coach Assignment',
              'Test coach table format: StationCode | Coach | Onboarding | Deboarding | Vacant',
              Icons.table_chart,
              () => _testCAMultiCoachAssignment(),
              Colors.brown,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'No Passenger Activity',
              'Test simplified notifications when no passengers boarding/deboarding',
              Icons.person_off,
              () => _testCANoPassengerActivity(),
              Colors.blueGrey,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'Anti-Duplication Logic',
              'Test duplicate notification prevention',
              Icons.block,
              () => _testCAAntiDuplication(),
              Colors.red.shade700,
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'FCM Token Test',
              'Test FCM token generation and Firestore sync',
              Icons.token,
              () => _testCAFCMToken(),
              Colors.green.shade700,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickTestsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Tests',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _runQuickTest,
                icon: const Icon(Icons.flash_on),
                label: const Text('Quick Test (Phase 1 + 2)'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepPurple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _runFullTestSuite,
                icon: const Icon(Icons.playlist_play),
                label: const Text('Run Full Test Suite'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.brown,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            _buildTestButton(
              'REAL Data Pipeline Test',
              'Test complete notification flow with real user data, GPS coordinates, and actual API calls',
              Icons.gps_fixed,
              () => _testRealDataPipeline(),
              Colors.green.shade700,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Results',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_lastTestResult.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _lastTestResult,
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              ),
            if (_testResults.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text('Test Suite Results:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              ..._testResults.entries.map((entry) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Icon(
                          entry.value ? Icons.check_circle : Icons.error,
                          color: entry.value ? Colors.green : Colors.red,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(entry.key)),
                      ],
                    ),
                  )),
            ],
            if (_lastTestResult.isEmpty && _testResults.isEmpty)
              const Text(
                'No tests run yet. Click any test button above to see results.',
                style: TextStyle(color: Colors.grey),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onPressed,
    Color color,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : onPressed,
        icon: Icon(icon),
        label: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
            Text(subtitle, style: const TextStyle(fontSize: 12)),
          ],
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          alignment: Alignment.centerLeft,
        ),
      ),
    );
  }

  // Test Implementation Methods

  Future<void> _testApiIntegration() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing API integration with REAL data...';
    });

    try {
      // Get real user context first
      final userContext =
          await NotificationIntegrationHelper.getRealUserContext();

      // Validate token before making API call
      if (_tokenController.text.isEmpty) {
        setState(() {
          _lastTestResult = '''❌ API Integration Test Failed!

Error: No authentication token available.

Please ensure you are logged in and the user token is loaded.
Try refreshing the token using the refresh button.

Failed at: ${DateTime.now().toString()}''';
        });
        return;
      }

      // Use real coordinates instead of hardcoded ones
      final realLat = userContext['latitude'] as String;
      final realLng = userContext['longitude'] as String;
      final hasRealLocation = userContext['has_real_location'] as bool;

      if (kDebugMode) {
        print(
            '🧪 Testing API Integration with REAL endpoint and coordinates...');
        print(
            '🔑 Using token: ${_tokenController.text.substring(0, _tokenController.text.length > 20 ? 20 : _tokenController.text.length)}...');
        print(
            '📍 REAL Coordinates: $realLat, $realLng (GPS: $hasRealLocation)');
        print('🚂 Train Number: ${userContext['train_number']}');
        print('👤 User ID: ${userContext['user_id']}');
      }

      // STEP 1: Check user's current train assignment status
      await _checkUserTrainAssignment();

      // Use REAL coordinates and data instead of text field values
      final response = await NotificationIntegrationHelper
          .fetchUpcomingStationDetailsWithNotifications(
        lat: realLat,
        lng: realLng,
        token: _tokenController.text,
        enableBoardingNotifications: true,
        enableOffBoardingNotifications: true,
        enableStationApproachingNotifications: true,
      );

      setState(() {
        _lastTestResult = '''✅ API Integration Test with REAL Data Successful!

REAL Data Used:
- Coordinates: $realLat, $realLng (GPS Location: $hasRealLocation)
- Train Number: ${userContext['train_number']}
- User ID: ${userContext['user_id']}

API Response:
- Train Number: ${response.trainNumber}
- Date: ${response.date}
- Message: ${response.message}
- Stations: ${response.stations.join(', ')}
- Coach Numbers: ${response.coachNumbers.join(', ')}

This test used REAL coordinates and user context instead of hardcoded test values.
Notifications triggered based on ACTUAL API response data from /microservice/train/location/.
Check your device notifications!

Response received at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('✅ API Integration test completed successfully');
        print(
            'Train: ${response.trainNumber}, Stations: ${response.stations.length}');
      }
    } catch (e) {
      // Enhanced error handling for authentication issues
      String errorMessage = e.toString();
      String troubleshooting = '';

      if (errorMessage.contains('401') ||
          errorMessage.contains('unauthorized') ||
          errorMessage.contains('token')) {
        troubleshooting = '''
🔑 AUTHENTICATION ERROR DETECTED:
This appears to be a token authentication issue.

Troubleshooting steps:
1. Click the refresh button (↻) to reload your user token
2. Ensure you are logged into the app
3. Check if your session has expired
4. Try logging out and logging back in

Current token status: $_userTokenStatus''';
      } else if (errorMessage.contains('Network') ||
          errorMessage.contains('connection')) {
        troubleshooting = '''
🌐 NETWORK ERROR DETECTED:
Check your internet connection and try again.''';
      } else {
        troubleshooting = '''
Please check:
- Network connection
- API endpoint availability
- Token validity
- Coordinates format''';
      }

      setState(() {
        _lastTestResult = '''❌ API Integration Test Failed!

Error: $errorMessage

$troubleshooting

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ API Integration test failed: $e');
        print('🔍 Token status: $_userTokenStatus');
        print('🔍 Token length: ${_tokenController.text.length}');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testPhase1Notification(String type) async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing Phase 1 notification with REAL data: $type...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing Phase 1 notification with REAL data: $type');
      }

      // Get real user context first
      final userContext =
          await NotificationIntegrationHelper.getRealUserContext();

      if (!userContext['has_user_context']) {
        setState(() {
          _lastTestResult = '''❌ Phase 1 Test Failed!

Type: ${type.toUpperCase()}
Error: Missing user context
Details: ${userContext['error'] ?? 'User ID or train number not found'}

User Context:
- User ID: ${userContext['user_id'] ?? 'Not found'}
- Train Number: ${userContext['train_number'] ?? 'Not found'}
- Has Real Location: ${userContext['has_real_location']}
- Coordinates: ${userContext['latitude']}, ${userContext['longitude']}

Please ensure you are logged in and have a train assignment.

Failed at: ${DateTime.now().toString()}''';
        });
        return;
      }

      // Test the REAL notification pipeline instead of mock notifications
      final result =
          await NotificationIntegrationHelper.testRealNotificationPipeline();

      if (result['success']) {
        setState(() {
          _lastTestResult = '''✅ Phase 1 REAL Notification Test Successful!

Type: ${type.toUpperCase()}
Status: ${result['status']}
Message: ${result['message']}

REAL Data Used:
- User ID: ${result['request_data']['user_id']}
- Train Number: ${result['request_data']['train_number']}
- Date: ${result['request_data']['date']}
- Coordinates: ${result['request_data']['coordinates']}
- FCM Token Available: ${result['request_data']['fcm_token_available']}
- Real Location: ${userContext['has_real_location']}

This notification was sent through the ACTUAL production pipeline:
1. Real user context → 2. Firebase Cloud Function → 3. Train Location API → 4. FCM delivery

Check your device notifications!

Sent at: ${DateTime.now().toString()}''';
        });
      } else {
        setState(() {
          _lastTestResult = '''⚠️ Phase 1 REAL Notification Test Result:

Type: ${type.toUpperCase()}
Status: ${result['status'] ?? 'Unknown'}
Message: ${result['message'] ?? 'No message'}

Details: ${result['details'] ?? 'No additional details'}

This used REAL data but notification was not sent (likely due to no passenger activity or anti-duplication).

REAL Data Used:
- User ID: ${result['request_data']?['user_id'] ?? 'N/A'}
- Train Number: ${result['request_data']?['train_number'] ?? 'N/A'}
- Coordinates: ${result['request_data']?['coordinates'] ?? 'N/A'}

Completed at: ${DateTime.now().toString()}''';
        });
      }

      if (kDebugMode) {
        print(
            '✅ Phase 1 REAL $type notification test completed: ${result['status']}');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ Phase 1 REAL Test Failed!

Type: ${type.toUpperCase()}
Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ Phase 1 REAL $type notification test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testPhase2Notification(String type) async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing Phase 2 notification with REAL data: $type...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing Phase 2 notification with REAL data: $type');
      }

      // Get real user context first
      final userContext =
          await NotificationIntegrationHelper.getRealUserContext();

      if (!userContext['has_user_context']) {
        setState(() {
          _lastTestResult = '''❌ Phase 2 Test Failed!

Type: ${type.toUpperCase()}
Error: Missing user context
Details: ${userContext['error'] ?? 'User ID or train number not found'}

User Context:
- User ID: ${userContext['user_id'] ?? 'Not found'}
- Train Number: ${userContext['train_number'] ?? 'Not found'}
- Has Real Location: ${userContext['has_real_location']}
- Coordinates: ${userContext['latitude']}, ${userContext['longitude']}

Please ensure you are logged in and have a train assignment.

Failed at: ${DateTime.now().toString()}''';
        });
        return;
      }

      // Test the REAL notification pipeline with different coordinates for variety
      Map<String, dynamic> result;
      String testDescription;

      switch (type) {
        case 'proximity':
          // Test with real coordinates
          result = await NotificationIntegrationHelper
              .testRealNotificationPipeline();
          testDescription =
              'Proximity-based notification using real GPS coordinates';
          break;
        case 'approach':
          // Test with slightly different coordinates to simulate movement
          final lat = double.parse(userContext['latitude']);
          final lng = double.parse(userContext['longitude']);
          result =
              await NotificationIntegrationHelper.testRealNotificationPipeline(
            customLat: (lat + 0.01).toString(), // Simulate movement
            customLng: (lng + 0.01).toString(),
          );
          testDescription =
              'Station approach notification with simulated movement';
          break;
        case 'status':
          // Test with real train number
          result =
              await NotificationIntegrationHelper.testRealNotificationPipeline(
            customTrainNumber: userContext['train_number'],
          );
          testDescription = 'Train status update using real train number';
          break;
        case 'count':
          // Test with real coordinates and train number
          result = await NotificationIntegrationHelper
              .testRealNotificationPipeline();
          testDescription =
              'Passenger count update using real train location data';
          break;
        default:
          result = await NotificationIntegrationHelper
              .testRealNotificationPipeline();
          testDescription = 'Enhanced notification test with real data';
      }

      if (result['success']) {
        setState(() {
          _lastTestResult = '''✅ Phase 2 REAL Notification Test Successful!

Type: ${type.toUpperCase()}
Test: $testDescription
Status: ${result['status']}
Message: ${result['message']}

REAL Data Used:
- User ID: ${result['request_data']['user_id']}
- Train Number: ${result['request_data']['train_number']}
- Date: ${result['request_data']['date']}
- Coordinates: ${result['request_data']['coordinates']}
- FCM Token Available: ${result['request_data']['fcm_token_available']}
- Real Location: ${userContext['has_real_location']}

This notification was sent through the ACTUAL production pipeline:
1. Real user context → 2. Firebase Cloud Function → 3. Train Location API → 4. FCM delivery

Check your device notifications!

Sent at: ${DateTime.now().toString()}''';
        });
      } else {
        setState(() {
          _lastTestResult = '''⚠️ Phase 2 REAL Notification Test Result:

Type: ${type.toUpperCase()}
Test: $testDescription
Status: ${result['status'] ?? 'Unknown'}
Message: ${result['message'] ?? 'No message'}

Details: ${result['details'] ?? 'No additional details'}

This used REAL data but notification was not sent (likely due to no passenger activity or anti-duplication).

REAL Data Used:
- User ID: ${result['request_data']?['user_id'] ?? 'N/A'}
- Train Number: ${result['request_data']?['train_number'] ?? 'N/A'}
- Coordinates: ${result['request_data']?['coordinates'] ?? 'N/A'}

Completed at: ${DateTime.now().toString()}''';
        });
      }

      if (kDebugMode) {
        print(
            '✅ Phase 2 REAL $type notification test completed: ${result['status']}');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ Phase 2 REAL Test Failed!

Type: ${type.toUpperCase()}
Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ Phase 2 REAL $type notification test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testRealDataPipeline() async {
    setState(() {
      _isLoading = true;
      _lastTestResult =
          'Testing REAL Data Pipeline - Complete notification flow...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing REAL Data Pipeline - Complete notification flow...');
      }

      // Get real user context
      final userContext =
          await NotificationIntegrationHelper.getRealUserContext();

      if (!userContext['has_user_context']) {
        setState(() {
          _lastTestResult = '''❌ REAL Data Pipeline Test Failed!

Error: Missing user context
Details: ${userContext['error'] ?? 'User ID or train number not found'}

User Context:
- User ID: ${userContext['user_id'] ?? 'Not found'}
- Train Number: ${userContext['train_number'] ?? 'Not found'}
- Has Real Location: ${userContext['has_real_location']}
- Coordinates: ${userContext['latitude']}, ${userContext['longitude']}

Please ensure you are logged in and have a train assignment.

Failed at: ${DateTime.now().toString()}''';
        });
        return;
      }

      // Test the complete REAL notification pipeline
      final result =
          await NotificationIntegrationHelper.testRealNotificationPipeline();

      if (result['success']) {
        setState(() {
          _lastTestResult = '''✅ REAL Data Pipeline Test Successful!

🎯 COMPLETE NOTIFICATION FLOW TESTED:
1. ✅ Real user context retrieved
2. ✅ Real GPS coordinates obtained
3. ✅ Firebase Cloud Function called
4. ✅ Train Location API queried
5. ✅ FCM notification sent

REAL Data Used:
- User ID: ${result['request_data']['user_id']}
- Train Number: ${result['request_data']['train_number']}
- Date: ${result['request_data']['date']}
- Coordinates: ${result['request_data']['coordinates']}
- FCM Token Available: ${result['request_data']['fcm_token_available']}
- Real GPS Location: ${userContext['has_real_location']}

Pipeline Status: ${result['status']}
Message: ${result['message']}

🔄 This test demonstrates the difference between:
❌ OLD: Static test values (hardcoded coordinates, mock train numbers)
✅ NEW: Dynamic real data (actual GPS, real user context, production API calls)

The notification was sent through the ACTUAL production pipeline used by the app!
Check your device notifications!

Completed at: ${DateTime.now().toString()}''';
        });
      } else {
        setState(() {
          _lastTestResult = '''⚠️ REAL Data Pipeline Test Result:

🎯 COMPLETE NOTIFICATION FLOW TESTED:
1. ✅ Real user context retrieved
2. ✅ Real GPS coordinates obtained
3. ✅ Firebase Cloud Function called
4. ✅ Train Location API queried
5. ⚠️ Notification not sent (see details below)

Status: ${result['status'] ?? 'Unknown'}
Message: ${result['message'] ?? 'No message'}

Details: ${result['details'] ?? 'No additional details'}

REAL Data Used:
- User ID: ${result['request_data']?['user_id'] ?? 'N/A'}
- Train Number: ${result['request_data']?['train_number'] ?? 'N/A'}
- Coordinates: ${result['request_data']?['coordinates'] ?? 'N/A'}
- Real GPS Location: ${userContext['has_real_location']}

🔄 This test demonstrates the difference between:
❌ OLD: Static test values (hardcoded coordinates, mock train numbers)
✅ NEW: Dynamic real data (actual GPS, real user context, production API calls)

The pipeline worked correctly but notification was not sent (likely due to no passenger activity or anti-duplication logic).

Completed at: ${DateTime.now().toString()}''';
        });
      }

      if (kDebugMode) {
        print('✅ REAL Data Pipeline test completed: ${result['status']}');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ REAL Data Pipeline Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ REAL Data Pipeline test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _runQuickTest() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Running quick test suite...';
    });

    try {
      if (kDebugMode) {
        print('🚀 Running quick notification test suite...');
      }

      await NotificationTestRunner.quickTest();

      setState(() {
        _lastTestResult = '''✅ Quick Test Suite Completed!

Tests run:
- Phase 1 basic notification
- Phase 2 proximity notification

All tests completed successfully.
Check your device notifications!

Completed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('✅ Quick test suite completed successfully');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ Quick Test Suite Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ Quick test suite failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _runFullTestSuite() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Running full test suite...';
      _testResults.clear();
    });

    try {
      if (kDebugMode) {
        print('🧪 Running full notification test suite...');
      }

      final results = await NotificationTestRunner.runAllTests();
      final passedTests = results.values.where((result) => result).length;
      final totalTests = results.length;

      setState(() {
        _testResults = results;

        _lastTestResult = '''✅ Full Test Suite Completed!

Results: $passedTests/$totalTests tests passed

Test Categories:
- Phase 1 Basic Setup: ${results['phase1_basic_setup'] == true ? '✅' : '❌'}
- Phase 1 Configuration: ${results['phase1_configuration'] == true ? '✅' : '❌'}
- Phase 1 Notifications: ${results['phase1_notifications'] == true ? '✅' : '❌'}
- Phase 2 Enhanced Types: ${results['phase2_enhanced_types'] == true ? '✅' : '❌'}
- Phase 2 Configuration: ${results['phase2_configuration'] == true ? '✅' : '❌'}
- Phase 2 Notifications: ${results['phase2_notifications'] == true ? '✅' : '❌'}
- Integration Compatibility: ${results['integration_compatibility'] == true ? '✅' : '❌'}
- All Notification Types: ${results['integration_all_types'] == true ? '✅' : '❌'}

Completed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('✅ Full test suite completed: $passedTests/$totalTests passed');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ Full Test Suite Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ Full test suite failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // CA/CS/EHK Testing Methods

  Future<void> _testCAMultiStationProximity() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing CA multi-station proximity...';
    });

    try {
      if (kDebugMode) {
        print(
            '🧪 Testing CA Multi-Station Proximity: ARA → BTA → DNR → PNBE → RJPB → PNC');
      }

      final result =
          await CANotificationTestService.executeComprehensiveTestSuite(
        trainNumber: _trainNumber,
        userId: _userId,
        coaches: _selectedCoaches,
      );

      setState(() {
        _lastTestResult = '''✅ CA Multi-Station Proximity Test Completed!

Route: ARA → BTA → DNR → PNBE → RJPB → PNC
Status: ${result['status']}
Message: ${result['message']}

Test Details:
${result['details'] ?? 'No additional details available'}

Completed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print(
            '✅ CA Multi-station proximity test completed: ${result['status']}');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ CA Multi-Station Proximity Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ CA Multi-station proximity test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testCAMultiCoachAssignment() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing CA multi-coach assignment...';
    });

    try {
      if (kDebugMode) {
        print(
            '🧪 Testing CA Multi-Coach Assignment for coaches: ${_selectedCoaches.join(', ')}');
      }

      final result = await CANotificationTestService.testSingleStation(
        station: _selectedStation,
        trainNumber: _trainNumber,
        userId: _userId,
        coaches: _selectedCoaches,
      );

      setState(() {
        _lastTestResult = '''✅ CA Multi-Coach Assignment Test Completed!

Station: $_selectedStation
Coaches: ${_selectedCoaches.join(', ')}
Status: ${result['status']}
Message: ${result['message']}

Expected notification format:
StationCode | Coach | Onboarding | Deboarding | Vacant
$_selectedStation | A1 | 5 | 3 | 2
$_selectedStation | B3 | 6 | 3 | 6

Completed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print(
            '✅ CA Multi-coach assignment test completed: ${result['status']}');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ CA Multi-Coach Assignment Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ CA Multi-coach assignment test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testCANoPassengerActivity() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing CA no passenger activity...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing CA No Passenger Activity Notification');
      }

      final result = await CANotificationTestService.testSingleStation(
        station: _selectedStation,
        trainNumber: _trainNumber,
        userId: _userId,
        coaches: _selectedCoaches,
      );

      setState(() {
        _lastTestResult = '''✅ CA No Passenger Activity Test Completed!

Status: ${result['status']}
Message: ${result['message']}

Test Details:
${result['details'] ?? 'No additional details available'}

Completed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('✅ CA No passenger activity test completed: ${result['status']}');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ CA No Passenger Activity Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ CA No passenger activity test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testCAAntiDuplication() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing CA anti-duplication logic...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing CA Anti-Duplication Logic');
      }

      final result =
          await CANotificationTestService.executeComprehensiveTestSuite(
        trainNumber: _trainNumber,
        userId: _userId,
        coaches: _selectedCoaches,
      );

      setState(() {
        _lastTestResult = '''✅ CA Anti-Duplication Test Completed!

Station: $_selectedStation
Status: ${result['status']}
Message: ${result['message']}

Anti-duplication logic: ${result['anti_duplication_working'] == true ? 'WORKING' : 'FAILED'}

Test Details:
${result['details'] ?? 'No additional details available'}

Completed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('✅ CA Anti-duplication test completed: ${result['status']}');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ CA Anti-Duplication Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ CA Anti-duplication test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testCAFCMToken() async {
    setState(() {
      _isLoading = true;
      _lastTestResult = 'Testing CA FCM token functionality...';
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing CA FCM Token functionality');
      }

      final result =
          await CANotificationTestService.testFCMTokenFunctionality();

      setState(() {
        _lastTestResult = '''✅ CA FCM Token Test Completed!

Status: ${result['status']}
Token Available: ${result['token_available'] == true ? 'YES' : 'NO'}
Token Length: ${result['token_length'] ?? 0} characters
Firestore Sync: ${result['firestore_sync'] == true ? 'SUCCESS' : 'FAILED'}

Token Preview: ${result['token_preview'] ?? 'N/A'}

Completed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('✅ CA FCM token test completed: ${result['status']}');
      }
    } catch (e) {
      setState(() {
        _lastTestResult = '''❌ CA FCM Token Test Failed!

Error: $e

Failed at: ${DateTime.now().toString()}''';
      });

      if (kDebugMode) {
        print('❌ CA FCM token test failed: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
